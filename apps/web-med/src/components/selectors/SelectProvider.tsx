"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Provider } from "@/hooks/selectors/use-select-provider";

import { ProviderStatus } from "@/api";
import PreviewProvider from "@/components/shared/PreviewProvider";
import { useSelectProvider } from "@/hooks/selectors/use-select-provider";

const i18n = {
  en: {
    label: "Provider",
    description: "Medical professional",
    placeholder: "Select a provider",
  },
};

// Re-export types for backward compatibility
type PartialProvider = Provider;
export type ProviderStructure = Provider;
type PartialProviderSelector = Omit<SelectorProps<Provider>, "data">;

/**
 * Base component props for provider selector
 */
export interface SelectProviderProps extends PartialProviderSelector {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter providers by organization ID */
  organizationId?: string;
  /** Filter providers by status */
  status?: ProviderStatus;
  /** Filter providers by specialty */
  specialties?: string[];
  /** Filter providers by location ID */
  locationId?: string;
  /** Filter providers by facility ID */
  facilityId?: string;
  /** Filter providers by minimum rating */
  minRating?: number;
  /** Filter providers by experience level (years) */
  minExperience?: number;
  /** Filter providers by availability status */
  availabilityStatus?: "AVAILABLE" | "BUSY" | "OFF_DUTY";
  /** Include person data */
  includePerson?: boolean;
  /** Include qualifications data */
  includeQualifications?: boolean;
  /** Include verification data */
  includeVerification?: boolean;
  /** Include reviews data */
  includeReviews?: boolean;
  /** Include experiences data */
  includeExperiences?: boolean;
  /** Include specialties data */
  includeSpecialties?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected provider */
  defaultSelection?: Provider;
  /** Default options to pre-populate */
  defaultOptions?: Provider[];
  /** Page size for pagination */
  pageSize?: number;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Size of the selector */
  size?: PartialProviderSelector["size"];
}

/**
 * Core props for provider selector (pure presentation component)
 */
interface SelectProviderCoreProps extends PartialProviderSelector {
  data?: Provider[];
  loading?: boolean;
  selection?: Provider;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (provider: Provider) => void | Promise<void>;
  onClear?: () => void;
}

/**
 * Pure presentation component for provider selector
 */
function SelectProviderCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectProviderCoreProps) {
  const enhancedData = useMemo(() => {
    const providers = [...data];
    if (
      selection &&
      !providers.find((provider) => provider.id === selection.id)
    ) {
      providers.unshift(selection);
    }
    return providers;
  }, [data, selection]);

  const handleRenderValue = useCallback(
    (provider: Provider) => {
      if (provider.person) {
        return `${provider.person.firstName} ${provider.person.lastName}`;
      }
      return provider.title ?? placeholder;
    },
    [placeholder],
  );

  const handleRenderItem = useCallback(
    (provider: Provider) => {
      return (
        <PreviewProvider
          loading={loading}
          provider={provider}
          size={size === "xl" ? "lg" : size || "lg"}
        />
      );
    },
    [loading, size],
  );

  const renderLoading = useCallback(() => {
    return <PreviewProvider loading={loading} />;
  }, [loading]);

  return (
    <Selector<Provider>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
    >
      {children}
    </Selector>
  );
}

/**
 * Provider selector component (data wrapper)
 */
export function SelectProvider({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  organizationId,
  status = ProviderStatus.ACTIVE,
  specialties,
  locationId,
  facilityId,
  minRating,
  minExperience,
  availabilityStatus,
  includePerson = true,
  includeQualifications = false,
  includeVerification = false,
  includeReviews = false,
  includeExperiences = false,
  includeSpecialties = false,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  ...props
}: SelectProviderProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectProvider({
    enabled,
    organizationId,
    status,
    specialties,
    locationId,
    facilityId,
    minRating,
    minExperience,
    availabilityStatus,
    includePerson,
    includeQualifications,
    includeVerification,
    includeReviews,
    includeExperiences,
    includeSpecialties,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectProviderCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

/**
 * Form field props for provider selector
 */
export interface SelectProviderFieldProps extends SelectProviderProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

/**
 * Form field component for provider selector
 */
export function SelectProviderField({
  name = "provider",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectProviderFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectProvider
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for provider selector
 */
export interface SearchProviderProps extends SelectProviderProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

/**
 * Search component for provider selector
 */
export function SearchProvider({
  group,
  name = "provider",
  defaultValue,
  enabled = true,
  loading = false,
  organizationId,
  status = ProviderStatus.ACTIVE,
  specialties,
  locationId,
  facilityId,
  minRating,
  minExperience,
  availabilityStatus,
  includePerson = true,
  includeQualifications = false,
  includeVerification = false,
  includeReviews = false,
  includeExperiences = false,
  includeSpecialties = false,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  onSelect,
  useDialog = false,
  ...props
}: SearchProviderProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectProvider({
    enabled,
    organizationId,
    status,
    specialties,
    locationId,
    facilityId,
    minRating,
    minExperience,
    availabilityStatus,
    includePerson,
    includeQualifications,
    includeVerification,
    includeReviews,
    includeExperiences,
    includeSpecialties,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Provider>({
    name,
    group,
    defaultValue,
    data: data ?? [],
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (provider: Provider) => {
      await setSelection(provider);
      await onSelect?.(provider);
      onSelectionChange(provider);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectProviderCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
    />
  );
}
