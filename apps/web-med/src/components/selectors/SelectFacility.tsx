"use client";

import { useCallback } from "react";

import type {
  SearchLocationProps as SearchLocationBaseProps,
  SelectLocationProps as SelectLocationBaseProps,
  SelectLocationFieldProps as SelectLocationFieldBaseProps,
} from "@axa/ui/selectors/SelectLocation";
import {
  SearchLocation as SearchLocationBase,
  SelectLocation as SelectLocationBase,
  SelectLocationField as SelectLocationFieldBase,
} from "@axa/ui/selectors/SelectLocation";

import type { Facility } from "@/hooks/selectors/use-select-facility";

import { useSelectFacility } from "@/hooks/selectors/use-select-facility";

export type { Facility };
export type FacilityStructure = Facility;

const i18n = {
  en: {
    label: "Facility",
    description: "Select a facility",
    placeholder: "Select a facility",
  },
};

interface FacilityAsLocation {
  id: string;
  name: string;
  address: {
    formatted: string | null;
    street?: string | null;
    city?: string | null;
    state?: string | null;
    postal?: string | null;
    country?: string | null;
  };
  [key: string]: unknown;
}

/**
 * Facility selector component props
 */
export interface SelectFacilityProps
  extends Omit<
    SelectLocationBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  defaultQuery?: string;
  defaultSelection?: Facility;
  defaultDebounce?: number;
  pageSize?: number;
  onSelect?: (facility: Facility) => void | Promise<void>;
}

export function SelectFacility({
  loading = false,
  enabled = true,
  organizationId,
  defaultQuery = "",
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  onSelect,
  ...props
}: SelectFacilityProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectFacility({
    enabled,
    organizationId,
    defaultQuery,
    defaultSelection,
    defaultDebounce,
    pageSize,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectLocationBase<FacilityAsLocation>
      {...props}
      data={data}
      loading={isLoading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      selection={selection}
      onValueChange={setQuery}
      onSelect={useCallback(
        async (facilityAsLocation: FacilityAsLocation) => {
          // Find the original facility from the hook data
          const originalFacility = data.find(
            (f) => f.id === facilityAsLocation.id,
          );
          if (originalFacility) {
            await setSelection(originalFacility);
          }
        },
        [setSelection, data],
      )}
    />
  );
}

/**
 * Form field props for facility selector
 */
export interface SelectFacilityFieldProps
  extends Omit<
    SelectLocationFieldBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter by organization ID */
  organizationId?: string;
  /** Page size for pagination */
  pageSize?: number;
  /** Field name in form */
  name?: string;
  /** Field label (default: "Facility") */
  label?: string;
  /** Field description (default: "Select a facility") */
  description?: string;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
  /** Callback when facility is selected */
  onSelect?: (facility: Facility) => void | Promise<void>;
}

/**
 * Form field component for facility selector - uses pre-built SelectLocationField with data fetching
 */
export function SelectFacilityField({
  enabled = true,
  loading,
  organizationId,
  pageSize = 5,
  name = "facilityId",
  label = i18n.en.label,
  description = i18n.en.description,
  showLabel = true,
  showDescription = true,
  onSelect,
  ...props
}: SelectFacilityFieldProps) {
  const { data, loading: hookLoading } = useSelectFacility({
    enabled,
    organizationId,
    pageSize,
  });

  const isLoading = loading ?? hookLoading;

  return (
    <SelectLocationFieldBase<FacilityAsLocation>
      {...props}
      name={name}
      label={label}
      description={description}
      showLabel={showLabel}
      showDescription={showDescription}
      data={data}
      loading={isLoading}
      onSelect={async (facilityAsLocation: FacilityAsLocation) => {
        // Find the original facility from the hook data
        const originalFacility = data.find(
          (f) => f.id === facilityAsLocation.id,
        );
        if (originalFacility && onSelect) {
          await onSelect(originalFacility);
        }
      }}
    />
  );
}

/**
 * Search component props for facility selector
 */
export interface SearchFacilityProps
  extends Omit<
    SearchLocationBaseProps<FacilityAsLocation>,
    "data" | "onSelect"
  > {
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter by organization ID */
  organizationId?: string;
  /** Page size for pagination */
  pageSize?: number;
  /** Search group identifier */
  group: string;
  /** Field name */
  name?: string;
  /** Default value */
  defaultValue?: string;
  /** Callback when facility is selected */
  onSelect?: (facility: Facility) => void | Promise<void>;
}

/**
 * Search component for facility selector - uses pre-built SearchLocation with data fetching
 * FIXED: Now properly passes hook data instead of empty array
 */
export function SearchFacility({
  enabled = true,
  organizationId,
  pageSize = 5,
  group,
  name = "facility",
  defaultValue,
  onSelect,
  ...props
}: SearchFacilityProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    setSelection,
  } = useSelectFacility({
    enabled,
    organizationId,
    pageSize,
  });

  return (
    <SearchLocationBase<FacilityAsLocation>
      {...props}
      name={name}
      group={group}
      selection={selection}
      defaultValue={defaultValue}
      data={data}
      loading={hookLoading}
      onSelect={useCallback(
        async (facilityAsLocation: FacilityAsLocation) => {
          // Find the original facility from the hook data
          const originalFacility = data.find(
            (f) => f.id === facilityAsLocation.id,
          );
          if (originalFacility && onSelect) {
            await onSelect(originalFacility);
            await setSelection(originalFacility);
          }
        },
        [onSelect, setSelection, data],
      )}
    />
  );
}
