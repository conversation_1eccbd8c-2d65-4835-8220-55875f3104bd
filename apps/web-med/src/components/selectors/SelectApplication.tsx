"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { ApplicationStatus } from "@/api";
import type { Application } from "@/hooks/selectors/use-select-application";

import { useSelectApplication } from "@/hooks/selectors/use-select-application";

const i18n = {
  en: {
    label: "Application",
    description: "Select an application",
    placeholder: "Select an application",
  },
};

// Re-export types for backward compatibility
export type ApplicationStructure = Application;
type PartialApplicationSelector = Omit<SelectorProps<Application>, "data">;

/**
 * Base component props for application selector
 */
export interface SelectApplicationProps extends PartialApplicationSelector {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter applications by job ID */
  jobId?: string;
  /** Filter applications by provider ID */
  providerId?: string;
  /** Filter applications by organization ID */
  organizationId?: string;
  /** Filter applications by status */
  status?: ApplicationStatus;
  /** Include provider data */
  includeProvider?: boolean;
  /** Include job data */
  includeJob?: boolean;
  /** Include organization data */
  includeOrganization?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected application */
  defaultSelection?: Application;
  /** Default options to pre-populate */
  defaultOptions?: Application[];
  /** Page size for pagination (default: 5) */
  pageSize?: number;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Size of the selector (default: "lg") */
  size?: PartialApplicationSelector["size"];
}

/**
 * Core props for application selector (pure presentation component)
 */
interface SelectApplicationCoreProps extends PartialApplicationSelector {
  data?: Application[];
  loading?: boolean;
  selection?: Application;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (application: Application) => void | Promise<void>;
  onClear?: () => void;
}

/**
 * Pure presentation component for application selector
 */
function SelectApplicationCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectApplicationCoreProps) {
  const enhancedData = useMemo(() => {
    const applications = [...data];
    if (
      selection &&
      !applications.find((application) => application.id === selection.id)
    ) {
      applications.unshift(selection);
    }
    return applications;
  }, [data, selection]);

  const handleRenderValue = useCallback((application: Application) => {
    const name = application.provider?.person
      ? `${application.provider.person.firstName} ${application.provider.person.lastName}`
      : "Unknown Provider";
    return name;
  }, []);

  const handleRenderItem = useCallback((application: Application) => {
    const name = application.provider?.person
      ? `${application.provider.person.firstName} ${application.provider.person.lastName}`
      : "Unknown Provider";
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{name}</div>
        <div className="text-sm text-muted-foreground">
          Status: {application.status}
        </div>
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Application>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
    >
      {children}
    </Selector>
  );
}

/**
 * Application selector component (data wrapper)
 */
export function SelectApplication({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  jobId,
  providerId,
  organizationId,
  status,
  includeProvider = true,
  includeJob = true,
  includeOrganization = false,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  ...props
}: SelectApplicationProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectApplication({
    enabled,
    jobId,
    providerId,
    organizationId,
    status,
    includeProvider,
    includeJob,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectApplicationCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

/**
 * Form field props for application selector
 */
export interface SelectApplicationFieldProps extends SelectApplicationProps {
  /** Field name in form */
  name?: string;
  /** Field label (default: "Application") */
  label?: string;
  /** Field description (default: "Select an application") */
  description?: string;
  /** Placeholder text (default: "Select an application") */
  placeholder?: string;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
}

/**
 * Form field component for application selector
 */
export function SelectApplicationField({
  name = "application",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectApplicationFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectApplication
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for application selector
 */
export interface SearchApplicationProps extends SelectApplicationProps {
  /** Search group identifier */
  group: string;
  /** Field name */
  name?: string;
  /** Default value (becomes defaultQuery) */
  defaultValue?: string;
}

/**
 * Search component for application selector
 */
export function SearchApplication({
  group,
  name = "application",
  defaultValue,
  enabled = true,
  loading = false,
  jobId,
  providerId,
  organizationId,
  status,
  includeProvider = true,
  includeJob = true,
  includeOrganization = false,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  onSelect,
  useDialog = false,
  ...props
}: SearchApplicationProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectApplication({
    enabled,
    jobId,
    providerId,
    organizationId,
    status,
    includeProvider,
    includeJob,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Application>(
    {
      name,
      group,
      defaultValue,
      data: data ?? [],
    },
  );

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (application: Application) => {
      await setSelection(application);
      await onSelect?.(application);
      onSelectionChange(application);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectApplicationCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
    />
  );
}
