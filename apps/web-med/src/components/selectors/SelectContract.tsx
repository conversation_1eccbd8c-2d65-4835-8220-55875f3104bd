"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { Contract } from "@/hooks/selectors/use-select-contract";

import { useSelectContract } from "@/hooks/selectors/use-select-contract";

const i18n = {
  en: {
    label: "Contract",
    description: "Select a contract",
    placeholder: "Select a contract",
  },
};

// Re-export types for backward compatibility
export type ContractStructure = Contract;
type PartialContractSelector = Omit<SelectorProps<Contract>, "data">;

/**
 * Base component props for contract selector
 */
export interface SelectContractProps extends PartialContractSelector {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter contracts by organization ID */
  organizationId?: string;
  /** Filter contracts by provider ID */
  providerId?: string;
  /** Filter contracts by status */
  status?: "PENDING" | "REJECTED" | "DRAFT" | "EXPIRED" | "SIGNED";
  /** Filter contracts by type */
  type?:
    | "OTHER"
    | "EMPLOYMENT"
    | "NON_COMPETE"
    | "NON_DISCLOSURE"
    | "SERVICE_RATE"
    | "SERVICE_AGREEMENT";
  /** Include provider data */
  includeProvider?: boolean;
  /** Include organization data */
  includeOrganization?: boolean;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected contract */
  defaultSelection?: Contract;
  /** Default options to pre-populate */
  defaultOptions?: Contract[];
  /** Page size for pagination */
  pageSize?: number;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Size of the selector */
  size?: PartialContractSelector["size"];
}

/**
 * Core props for contract selector (pure presentation component)
 */
interface SelectContractCoreProps extends PartialContractSelector {
  data?: Contract[];
  loading?: boolean;
  selection?: Contract;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (contract: Contract) => void | Promise<void>;
  onClear?: () => void;
}

/**
 * Pure presentation component for contract selector
 */
function SelectContractCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  ...props
}: SelectContractCoreProps) {
  const enhancedData = useMemo(() => {
    const contracts = [...data];
    if (
      selection &&
      !contracts.find((contract) => contract.id === selection.id)
    ) {
      contracts.unshift(selection);
    }
    return contracts;
  }, [data, selection]);

  const handleRenderValue = useCallback(
    (contract: Contract) => {
      return contract.title || placeholder;
    },
    [placeholder],
  );

  const handleRenderItem = useCallback((contract: Contract) => {
    return (
      <div className="flex flex-col gap-1 p-1 text-start">
        <div className="font-medium">{contract.title}</div>
        <div className="text-sm text-muted-foreground">
          Status: {contract.status}
        </div>
        {contract.organization?.name && (
          <div className="text-xs text-muted-foreground">
            Organization: {contract.organization.name}
          </div>
        )}
        {contract.provider?.person?.firstName && (
          <div className="text-xs text-muted-foreground">
            Provider: {contract.provider.person.firstName}{" "}
            {contract.provider.person.lastName}
          </div>
        )}
      </div>
    );
  }, []);

  const renderLoading = useCallback(() => {
    return (
      <div className="flex flex-col gap-1">
        <div className="font-medium">
          <Skeleton className="h-5 w-[200px]" />
        </div>
        <div className="text-sm text-muted-foreground">
          <Skeleton className="h-5 w-[200px]" />
        </div>
      </div>
    );
  }, []);

  return (
    <Selector<Contract>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-16 p-3": size === "lg",
          "h-14 p-2": size === "md",
          "h-12 p-1": size === "sm",
        },
        className,
      )}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      loading={loading}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={handleRenderItem}
      renderLoading={renderLoading}
      renderValue={handleRenderValue}
    >
      {children}
    </Selector>
  );
}

/**
 * Contract selector component (data wrapper)
 */
export function SelectContract({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  organizationId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeOrganization = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  ...props
}: SelectContractProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectContract({
    enabled,
    organizationId,
    providerId,
    status,
    type,
    includeProvider,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectContractCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

/**
 * Form field props for contract selector
 */
export interface SelectContractFieldProps extends SelectContractProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

/**
 * Form field component for contract selector
 */
export function SelectContractField({
  name = "contract",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectContractFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectContract
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for contract selector
 */
export interface SearchContractProps extends SelectContractProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

/**
 * Search component for contract selector
 */
export function SearchContract({
  group,
  name = "contract",
  defaultValue,
  enabled = true,
  loading = false,
  organizationId,
  providerId,
  status,
  type,
  includeProvider = true,
  includeOrganization = true,
  defaultQuery,
  defaultSelection,
  defaultOptions,
  pageSize = 5,
  defaultDebounce = 500,
  onSelect,
  useDialog = false,
  ...props
}: SearchContractProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectContract({
    enabled,
    organizationId,
    providerId,
    status,
    type,
    includeProvider,
    includeOrganization,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultOptions,
    pageSize,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<Contract>({
    name,
    group,
    defaultValue,
    data: data ?? [],
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (contract: Contract) => {
      await setSelection(contract);
      await onSelect?.(contract);
      onSelectionChange(contract);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectContractCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
    />
  );
}
