"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { PlusCircleIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { JobPost } from "@/hooks/selectors/use-select-job-post";

import { useSelectJobPost } from "@/hooks/selectors/use-select-job-post";

const i18n = {
  en: {
    label: "Job",
    description: "Select a job",
    placeholder: "Select a job",
    actions: {
      selectJob: "Select Job",
    },
  },
};

// Re-export types for backward compatibility
export type JobStructure = JobPost;
type PartialJobPostSelector = Omit<SelectorProps<JobPost>, "data">;

/**
 * Base component props for job post selector
 */
export interface SelectJobPostProps extends PartialJobPostSelector {
  /** Override loading state */
  loading?: boolean;
  /** Enable/disable the selector */
  enabled?: boolean;
  /** Filter jobs by organization ID */
  organizationId?: string;
  /** Default query string */
  defaultQuery?: string;
  /** Default selected job post */
  defaultSelection?: JobPost;
  /** Debounce delay in milliseconds */
  defaultDebounce?: number;
  /** Page size for pagination (default: 5) */
  pageSize?: number;
  /** Page number for pagination (default: 0) */
  pageNumber?: number;
  /** Show pending state */
  pending?: boolean;
  /** Size of the selector (default: "lg") */
  size?: PartialJobPostSelector["size"];
}

/**
 * Core props for job post selector (pure presentation component)
 */
interface SelectJobPostCoreProps extends PartialJobPostSelector {
  data?: JobPost[];
  loading?: boolean;
  selection?: JobPost;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (jobPost: JobPost) => void | Promise<void>;
  onClear?: () => void;
  pending?: boolean;
}

/**
 * Pure presentation component for job post selector
 */
function SelectJobPostCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  pending = false,
  ...props
}: SelectJobPostCoreProps) {
  const enhancedData = useMemo(() => {
    const jobPosts = [...data];
    if (selection && !jobPosts.find((jobPost) => jobPost.id === selection.id)) {
      jobPosts.unshift(selection);
    }
    return jobPosts;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: JobPost) => (
      <div className="flex flex-col gap-1 truncate text-start">
        <h3
          className={cn("truncate text-base font-semibold", {
            "text-sm": size === "sm",
            "text-base": size === "md",
            "text-lg": size === "lg",
          })}
        >
          {item.summary}
        </h3>
        <div
          className={cn("flex gap-2 truncate text-xs text-muted-foreground", {
            "text-xs": size === "sm",
            "text-sm": size === "md",
            "text-base": size === "lg",
          })}
        >
          <span>Role: {item.role}</span>
          <span>Status: {item.status}</span>
        </div>
        {item.organization && (
          <p
            className={cn("truncate text-sm text-muted-foreground", {
              "text-xs": size === "sm",
              "text-sm": size === "md",
              "text-base": size === "lg",
            })}
          >
            {item.organization.name}
          </p>
        )}
      </div>
    ),
    [size],
  );

  const renderLoading = useCallback(
    () => (
      <div className="flex flex-col gap-1 text-start">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
    ),
    [],
  );

  return (
    <Selector<JobPost>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-12 p-2": size === "md",
          "h-9 p-2": size === "sm",
          "min-h-8 py-2": size === "lg",
        },
        className,
      )}
      loading={loading}
      pending={pending}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={renderItem}
      renderLoading={renderLoading}
    >
      {children ?? (
        <>
          <PlusCircleIcon size="20" color="currentColor" />
          <span>{i18n.en.actions.selectJob}</span>
        </>
      )}
    </Selector>
  );
}

/**
 * Job post selector component (data wrapper)
 */
export function SelectJobPost({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  organizationId,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  ...props
}: SelectJobPostProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectJobPost({
    enabled,
    organizationId,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectJobPostCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

/**
 * Form field props for job selector
 */
export interface SelectJobPostFieldProps extends SelectJobPostProps {
  /** Field name in form */
  name?: string;
  /** Field label (default: "Job") */
  label?: string;
  /** Field description (default: "Select a job") */
  description?: string;
  /** Placeholder text (default: "Select a job") */
  placeholder?: string;
  /** Show field label (default: true) */
  showLabel?: boolean;
  /** Show field description (default: true) */
  showDescription?: boolean;
}

/**
 * Form field component for job selector
 */
export function SelectJobPostField({
  name = "job",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectJobPostFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectJobPost
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

/**
 * Search component props for job post selector
 */
export interface SearchJobPostProps extends SelectJobPostProps {
  /** Search group identifier */
  group: string;
  /** Field name */
  name?: string;
  /** Default value (becomes defaultQuery) */
  defaultValue?: string;
}

/**
 * Search component for job post selector
 */
export function SearchJobPost({
  group,
  name = "jobPost",
  defaultValue,
  enabled = true,
  loading = false,
  organizationId,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  onSelect,
  useDialog = false,
  ...props
}: SearchJobPostProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectJobPost({
    enabled,
    organizationId,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<JobPost>({
    name,
    group,
    defaultValue,
    data: data ?? [],
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (jobPost: JobPost) => {
      await setSelection(jobPost);
      await onSelect?.(jobPost);
      onSelectionChange(jobPost);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectJobPostCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
    />
  );
}
