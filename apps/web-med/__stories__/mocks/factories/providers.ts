/**
 * Provider Mock Factory
 *
 * Generates mock provider data using the database schema types.
 * Providers represent healthcare professionals in the system.
 */

import {
  AccountStatus,
  ProviderStatus,
  QualificationStatus,
  QualificationType,
  VerificationStatus,
} from "@axa/database-medical";

import type {
  DeepPartial,
  MockFactoryOptions,
  MockPaginatedResponse,
  MockProvider,
} from "../utils/types";

import { MockFactory, mockUtils } from "./core";

/**
 * Provider factory for generating mock provider data
 */
export const providerFactory = new MockFactory<MockProvider>(
  (overrides = {}) => {
    const id = (overrides as any).id || mockUtils.id();
    const personId = (overrides as any).personId || mockUtils.id();
    const addressId = (overrides as any).addressId || mockUtils.id();

    return {
      id,
      createdAt: mockHelpers.pastDate(365),
      updatedAt: mockHelpers.pastDate(30),
      deletedAt: null,

      // Stripe integration
      accountId: mockHelpers.maybe(() => mockHelpers.id(), 0.7),
      accountStatus: mockHelpers.enumValue(AccountStatus),

      // Verification
      verificationStatus: mockHelpers.enumValue(VerificationStatus),
      status: mockHelpers.enumValue(ProviderStatus),

      // Profile
      title: mockHelpers.maybe(() => mockHelpers.faker.person.jobTitle(), 0.8),
      gender: mockHelpers.maybe(
        () =>
          mockHelpers.faker.helpers.arrayElement(["Male", "Female", "Other"]),
        0.9,
      ),
      spokenLanguages: mockHelpers.faker.helpers.arrayElements(
        [
          "English",
          "Spanish",
          "French",
          "German",
          "Italian",
          "Portuguese",
          "Mandarin",
          "Japanese",
        ],
        { min: 1, max: 3 },
      ),

      score: mockHelpers.faker.number.float({
        min: 0,
        max: 100,
        fractionDigits: 1,
      }),

      // Calendar
      calendarId: mockHelpers.maybe(() => mockHelpers.id(), 0.6),

      // Required relationships
      personId,
      addressId,

      // Override any provided values
      ...overrides,
    } as MockProvider;
  },
);

/**
 * Provider verification factory
 */
export const providerVerificationFactory = new BaseFactory<any>(
  "providerVerification",
  (overrides = {}) => {
    const id = overrides.id || mockHelpers.id();
    const providerId = overrides.providerId || mockHelpers.id();

    const status =
      overrides.status || mockHelpers.enumValue(VerificationStatus);
    const isVerified = status === VerificationStatus.APPROVED;

    return {
      id,
      createdAt: mockHelpers.pastDate(180),
      updatedAt: mockHelpers.pastDate(30),
      deletedAt: null,

      // Verification dates
      verifiedAt: isVerified ? mockHelpers.pastDate(60) : null,
      i9VerifiedAt: isVerified ? mockHelpers.pastDate(90) : null,
      identityVerifiedAt: isVerified ? mockHelpers.pastDate(120) : null,
      backgroundVerifiedAt: isVerified ? mockHelpers.pastDate(150) : null,

      // Status fields
      status,
      backgroundCheckStatus: mockHelpers.enumValue(VerificationStatus),
      i9VerificationStatus: mockHelpers.enumValue(VerificationStatus),
      identityVerificationStatus: mockHelpers.enumValue(VerificationStatus),

      notes: mockHelpers.maybe(() => mockHelpers.faker.lorem.sentence(), 0.3),

      providerId,

      ...overrides,
    };
  },
);

/**
 * Provider settings factory
 */
export const providerSettingsFactory = new BaseFactory<any>(
  "providerSettings",
  (overrides = {}) => {
    const id = overrides.id || mockHelpers.id();
    const providerId = overrides.providerId || mockHelpers.id();

    return {
      id,
      createdAt: mockHelpers.pastDate(180),
      updatedAt: mockHelpers.pastDate(30),
      deletedAt: null,

      // Work preferences
      openToWork: mockHelpers.faker.datatype.boolean(0.8),
      openToOnCall: mockHelpers.faker.datatype.boolean(0.4),
      openToRelocate: mockHelpers.faker.datatype.boolean(0.3),
      openToTravel: mockHelpers.faker.datatype.boolean(0.5),

      // Distance preferences
      minRelocateDistance: mockHelpers.maybe(
        () => mockHelpers.faker.number.int({ min: 0, max: 50 }),
        0.3,
      ),
      maxRelocateDistance: mockHelpers.maybe(
        () => mockHelpers.faker.number.int({ min: 100, max: 500 }),
        0.3,
      ),
      minTravelDistance: mockHelpers.maybe(
        () => mockHelpers.faker.number.int({ min: 0, max: 25 }),
        0.5,
      ),
      maxTravelDistance: mockHelpers.maybe(
        () => mockHelpers.faker.number.int({ min: 50, max: 200 }),
        0.5,
      ),

      providerId,

      ...overrides,
    };
  },
);

/**
 * Qualification factory for provider qualifications
 */
export const qualificationFactory = new BaseFactory<any>(
  "qualification",
  (overrides = {}) => {
    const id = overrides.id || mockHelpers.id();
    const providerId = overrides.providerId || mockHelpers.id();
    const type = overrides.type || mockHelpers.enumValue(QualificationType);

    const qualificationNames = {
      [QualificationType.DEGREE]: [
        "Bachelor of Science in Nursing",
        "Master of Science in Nursing",
        "Doctor of Medicine",
        "Doctor of Pharmacy",
      ],
      [QualificationType.LICENSE]: [
        "Registered Nurse License",
        "Medical License",
        "Pharmacy License",
        "Physical Therapy License",
      ],
      [QualificationType.CERTIFICATE]: [
        "BLS Certification",
        "ACLS Certification",
        "PALS Certification",
        "CPR Certification",
      ],
      [QualificationType.OTHER]: [
        "Specialty Training",
        "Continuing Education",
        "Professional Development",
      ],
    };

    return {
      id,
      createdAt: mockHelpers.pastDate(365),
      updatedAt: mockHelpers.pastDate(30),
      deletedAt: null,
      approvedAt: mockHelpers.maybe(() => mockHelpers.pastDate(180), 0.7),
      rejectedAt: null,
      expiresAt: mockHelpers.maybe(() => mockHelpers.futureDate(730), 0.6),

      status: mockHelpers.enumValue(QualificationStatus),
      type,
      name: mockHelpers.faker.helpers.arrayElement(qualificationNames[type]),
      institution: mockHelpers.maybe(
        () => mockHelpers.faker.company.name(),
        0.8,
      ),
      identifier: mockHelpers.maybe(
        () => mockHelpers.faker.string.alphanumeric(8).toUpperCase(),
        0.7,
      ),
      state: mockHelpers.maybe(
        () => mockHelpers.faker.location.state({ abbreviated: true }),
        0.8,
      ),
      country: "US",

      startDate: mockHelpers.maybe(() => mockHelpers.pastDate(1825), 0.9), // ~5 years ago
      endDate: mockHelpers.maybe(() => mockHelpers.pastDate(365), 0.6), // ~1 year ago

      providerId,
      documentId: mockHelpers.maybe(() => mockHelpers.id(), 0.8),

      ...overrides,
    };
  },
);

/**
 * Specialty factory for provider specialties
 */
export const specialtyFactory = new BaseFactory<any>(
  "specialty",
  (overrides = {}) => {
    const id = overrides.id || mockHelpers.id();

    const specialties = [
      {
        name: "Registered Nurse",
        description: "General nursing care and patient support",
      },
      {
        name: "Nurse Practitioner",
        description: "Advanced practice nursing with prescriptive authority",
      },
      {
        name: "Physician Assistant",
        description: "Medical practice under physician supervision",
      },
      {
        name: "Emergency Medicine",
        description: "Acute care and emergency medical services",
      },
      {
        name: "Critical Care",
        description: "Intensive care unit and critical patient care",
      },
      {
        name: "Medical Surgical",
        description: "General medical and surgical nursing",
      },
      {
        name: "Pediatrics",
        description: "Healthcare for infants, children, and adolescents",
      },
      {
        name: "Obstetrics",
        description: "Pregnancy, childbirth, and postpartum care",
      },
      {
        name: "Cardiology",
        description: "Heart and cardiovascular system care",
      },
      { name: "Oncology", description: "Cancer treatment and care" },
    ];

    const specialty = mockHelpers.faker.helpers.arrayElement(specialties);

    return {
      id,
      createdAt: mockHelpers.pastDate(365),
      updatedAt: mockHelpers.pastDate(30),
      deletedAt: null,

      name: specialty.name,
      description: specialty.description,

      ...overrides,
    };
  },
);

/**
 * Provider presets for common scenarios
 */
export const providerPresets = {
  /**
   * Active verified provider
   */
  activeVerified: (overrides?: DeepPartial<MockProvider>) =>
    providerFactory.create({
      status: ProviderStatus.ACTIVE,
      verificationStatus: VerificationStatus.APPROVED,
      score: mockHelpers.faker.number.float({
        min: 85,
        max: 100,
        fractionDigits: 1,
      }),
      ...overrides,
    }),

  /**
   * Pending provider awaiting verification
   */
  pendingVerification: (overrides?: DeepPartial<MockProvider>) =>
    providerFactory.create({
      status: ProviderStatus.PENDING,
      verificationStatus: VerificationStatus.PENDING,
      score: mockHelpers.faker.number.float({
        min: 50,
        max: 75,
        fractionDigits: 1,
      }),
      ...overrides,
    }),

  /**
   * Suspended provider
   */
  suspended: (overrides?: DeepPartial<MockProvider>) =>
    providerFactory.create({
      status: ProviderStatus.SUSPENDED,
      verificationStatus: VerificationStatus.APPROVED,
      score: mockHelpers.faker.number.float({
        min: 20,
        max: 50,
        fractionDigits: 1,
      }),
      ...overrides,
    }),

  /**
   * High-rated experienced provider
   */
  experienced: (overrides?: DeepPartial<MockProvider>) =>
    providerFactory.create({
      status: ProviderStatus.ACTIVE,
      verificationStatus: VerificationStatus.APPROVED,
      score: mockHelpers.faker.number.float({
        min: 95,
        max: 100,
        fractionDigits: 1,
      }),
      title: "Senior Nurse Practitioner",
      spokenLanguages: ["English", "Spanish"],
      ...overrides,
    }),

  /**
   * New provider just starting
   */
  newProvider: (overrides?: DeepPartial<MockProvider>) =>
    providerFactory.create({
      status: ProviderStatus.ACTIVE,
      verificationStatus: VerificationStatus.APPROVED,
      score: mockHelpers.faker.number.float({
        min: 70,
        max: 85,
        fractionDigits: 1,
      }),
      createdAt: mockHelpers.pastDate(30), // Created within last month
      ...overrides,
    }),
};
