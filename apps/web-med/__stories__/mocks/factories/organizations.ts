import { db } from "@axa/database-medical/generated";

import {
  OrganizationBillingType,
  OrganizationClass,
  OrganizationMode,
  OrganizationStatus,
  OrganizationType,
} from "@/api";

import type { DeepPartial, MockOrganization } from "../utils/types";

import { applyOverrides, createFactory, mockUtils } from "./core";

function generateOrganization(
  overrides?: DeepPartial<MockOrganization>,
): MockOrganization {
  const baseOrganization = db.organization.create();

  return applyOverrides(baseOrganization, overrides);
}

export const organizationFactory = createFactory(
  generateOrganization,
  "organization",
);

export const organizationPresets = {
  activeClient: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      type: OrganizationType.CLIENT,
      class: OrganizationClass.PRIVATE,
      status: OrganizationStatus.ACTIVE,
      mode: OrganizationMode.INDEPENDENT,
      billing: OrganizationBillingType.INVOICE,
      name: "Regional Medical Center",
      ...overrides,
    }),
  staffingAccount: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      type: "ACCOUNT",
      class: "PRIVATE",
      status: "ACTIVE",
      mode: "INDEPENDENT",
      billing: "CHARGE",
      name: "MedStaff Solutions",
      ...overrides,
    }),
  governmentOrg: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      type: OrganizationType.CLIENT,
      class: "GOVERNMENT",
      status: "ACTIVE",
      mode: "ASSISTED",
      billing: "INVOICE",
      name: "County General Hospital",
      ...overrides,
    }),
  pending: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      status: "PENDING",
      mode: "ASSISTED",
      billing: "NONE",
      ...overrides,
    }),
  internal: (overrides?: DeepPartial<MockOrganization>) =>
    organizationFactory.create({
      type: "INTERNAL",
      class: "PRIVATE",
      status: "ACTIVE",
      mode: "INDEPENDENT",
      billing: "NONE",
      name: "AXA Internal",
      ...overrides,
    }),
};

export function generateMixedOrganizations(count = 10): MockOrganization[] {
  const types = ["INTERNAL", OrganizationType.CLIENT, "ACCOUNT"] as const;
  const classes = ["PRIVATE", "NONPROFIT", "GOVERNMENT"] as const;
  const statuses = ["ACTIVE", "INACTIVE", "PENDING"] as const;

  return Array.from({ length: count }, (_, index) => {
    const type = types[index % types.length];
    const orgClass = classes[index % classes.length];
    const status = statuses[index % statuses.length];

    return organizationFactory.create({
      type,
      class: orgClass,
      status,
    });
  });
}

export const organizationScenarios = {
  healthcareSystem: () => ({
    organization: organizationPresets.activeClient({
      name: "Regional Healthcare System",
      class: "PRIVATE",
    }),
    facilities: mockUtils.number(5, 15),
    members: mockUtils.number(20, 50),
    activeJobs: mockUtils.number(10, 30),
  }),
  startupAgency: () => ({
    organization: organizationPresets.staffingAccount({
      name: "MedStaff Solutions",
      status: "ACTIVE",
    }),
    facilities: 0,
    members: mockUtils.number(3, 10),
    activeJobs: mockUtils.number(5, 15),
  }),
  governmentNetwork: () => ({
    organization: organizationPresets.governmentOrg({
      name: "State Health Network",
    }),
    facilities: mockUtils.number(3, 8),
    members: mockUtils.number(5, 15),
    activeJobs: mockUtils.number(2, 10),
  }),
};
