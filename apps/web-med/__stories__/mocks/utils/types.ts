/**
 * Type definitions for the centralized mock system
 *
 * Uses the existing database types from @axa/database-medical/generated
 * to ensure perfect alignment with the actual database schema and API layer.
 */

import type { db } from "@axa/database-medical/generated";

import type { RouterOutputs } from "@/api";

// Base factory options
export interface MockFactoryOptions {
  /** Override specific fields */
  overrides?: Record<string, any>;
  /** Include related entities */
  include?: string[];
  /** Exclude specific fields */
  exclude?: string[];
  /** Use specific preset */
  preset?: string;
}

// Database entity types - extracted from the MSW factory
// These match the actual database schema exactly
export type MockOrganization = ReturnType<typeof db.organization.create>;
export type MockProvider = ReturnType<typeof db.provider.create>;
export type MockPerson = ReturnType<typeof db.person.create>;
export type MockJob = ReturnType<typeof db.jobpost.create>;
export type MockJobPosition = ReturnType<typeof db.jobposition.create>;
export type MockApplication = ReturnType<typeof db.application.create>;
export type MockShift = ReturnType<typeof db.shift.create>;
export type MockContract = ReturnType<typeof db.contract.create>;
export type MockAgreement = ReturnType<typeof db.agreement.create>;
export type MockOffer = ReturnType<typeof db.offer.create>;
export type MockLocation = ReturnType<typeof db.location.create>;
export type MockBuilding = ReturnType<typeof db.building.create>;
export type MockDepartment = ReturnType<typeof db.department.create>;
export type MockDocument = ReturnType<typeof db.document.create>;
export type MockInvoice = ReturnType<typeof db.invoice.create>;
export type MockPayout = ReturnType<typeof db.payout.create>;
export type MockIncident = ReturnType<typeof db.incident.create>;
export type MockSpecialty = ReturnType<typeof db.specialty.create>;
export type MockQualification = ReturnType<typeof db.qualification.create>;
export type MockAddress = ReturnType<typeof db.address.create>;
export type MockContact = ReturnType<typeof db.contact.create>;
export type MockReview = ReturnType<typeof db.review.create>;
export type MockSchedule = ReturnType<typeof db.schedule.create>;
export type MockTimeBlock = ReturnType<typeof db.timeblock.create>;
export type MockJobExperience = ReturnType<typeof db.jobexperience.create>;
export type MockThread = ReturnType<typeof db.thread.create>;
export type MockMessage = ReturnType<typeof db.message.create>;
export type MockValue = ReturnType<typeof db.value.create>;
export type MockAction = ReturnType<typeof db.action.create>;
export type MockReferral = ReturnType<typeof db.referral.create>;
export type MockOrganizationSetting = ReturnType<
  typeof db.organizationsetting.create
>;
export type MockProviderSetting = ReturnType<typeof db.providersetting.create>;
export type MockPersonSetting = ReturnType<typeof db.personsetting.create>;
export type MockProviderVerification = ReturnType<
  typeof db.providerverification.create
>;
export type MockSignature = ReturnType<typeof db.signature.create>;

// API response types for paginated results (keep these for tRPC compatibility)
export type MockOrganizations = RouterOutputs["organizations"]["getMany"];
export type MockProviders = RouterOutputs["providers"]["getMany"];
export type MockJobs = RouterOutputs["jobs"]["getMany"];
export type MockApplications = RouterOutputs["applications"]["getMany"];
export type MockShifts = RouterOutputs["shifts"]["getMany"];
export type MockContracts = RouterOutputs["contracts"]["getMany"];
export type MockOffers = RouterOutputs["offers"]["getMany"];
export type MockLocations = RouterOutputs["locations"]["getMany"];
export type MockDepartments = RouterOutputs["departments"]["getMany"];
export type MockPeople = RouterOutputs["people"]["getMany"];
export type MockUsers = RouterOutputs["user"]["getMany"];
export type MockDocuments = RouterOutputs["documents"]["getMany"];
export type MockInvoices = RouterOutputs["billing"]["invoices"]["getMany"];
export type MockPayouts = RouterOutputs["billing"]["payouts"]["getMany"];
export type MockIncidents = RouterOutputs["incidents"]["getMany"];
export type MockSpecialties = RouterOutputs["specialties"]["getMany"];

export type MockQualifications = RouterOutputs["qualifications"]["getMany"];
export type MockAddresses = RouterOutputs["addresses"]["getMany"];
export type MockContacts = RouterOutputs["contacts"]["getMany"];
export type MockReviews = RouterOutputs["reviews"]["getMany"];

// Pagination types
export interface MockPagination {
  pageIndex: number;
  pageSize: number;
}

export interface MockPaginatedResponse<T> {
  items: T[];
  total: number;
}

// Factory function types
export type MockFactory<T> = (
  overrides?: Partial<T>,
  options?: MockFactoryOptions,
) => T;
export type MockArrayFactory<T> = (
  count?: number,
  overrides?: Partial<T>,
  options?: MockFactoryOptions,
) => T[];
export type MockPaginatedFactory<T> = (
  count?: number,
  overrides?: Partial<T>,
  options?: MockFactoryOptions,
) => MockPaginatedResponse<T>;

// Handler types for MSW/tRPC
export interface MockQueryHandler {
  query: (input?: unknown) => unknown;
}

export interface MockMutationHandler {
  mutation: (input?: unknown) => unknown;
}

// Preset types
export interface MockPreset {
  name: string;
  description: string;
  data: Record<string, unknown>;
}

// Scenario types for complex test cases
export interface MockScenario {
  name: string;
  description: string;
  setup: () => Record<string, unknown>;
}

// State types for different entity states
export type EntityState =
  | "active"
  | "inactive"
  | "pending"
  | "approved"
  | "rejected"
  | "suspended"
  | "completed"
  | "cancelled";

// Relationship configuration
export interface RelationshipConfig {
  entity: string;
  count?: number;
  required?: boolean;
  factory?: string;
}

// Mock generation options
export interface GenerationOptions {
  seed?: number;
  locale?: string;
  relationships?: Record<string, RelationshipConfig>;
  constraints?: Record<string, unknown>;
}

// Story helper types
export interface StoryState<T> {
  loading: boolean;
  error: Error | null;
  data: T;
}

export interface TableStoryProps<T> {
  data: MockPaginatedResponse<T>;
  pagination: MockPagination;
  setPagination: (pagination: MockPagination) => void;
  loading?: boolean;
  error?: Error | null;
}

// Migration helper types
export interface MigrationHelper {
  from: string;
  to: string;
  converter: (oldData: unknown) => unknown;
}

// Validation types
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Performance tracking
export interface PerformanceMetrics {
  generationTime: number;
  memoryUsage: number;
  cacheHits: number;
  cacheMisses: number;
}

// Configuration types
export interface MockConfig {
  defaultCount: number;
  enableCaching: boolean;
  enablePerformanceTracking: boolean;
  defaultLocale: string;
  defaultSeed?: number;
}

// Export utility type helpers
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> &
  Partial<Pick<T, K>>;

// Mock data registry for tracking generated data
export interface MockRegistry {
  organizations: Map<string, MockOrganization>;
  providers: Map<string, MockProvider>;
  jobs: Map<string, MockJob>;
  applications: Map<string, MockApplication>;
  shifts: Map<string, MockShift>;
  // ... other entities
}

// Event types for mock system
export type MockEvent =
  | { type: "GENERATED"; entity: string; id: string; data: unknown }
  | { type: "CACHED"; entity: string; id: string }
  | { type: "ERROR"; entity: string; error: string };

export type MockEventListener = (event: MockEvent) => void;
