/**
 * tRPC Mock Handlers for MSW
 *
 * This module provides MSW handlers for mocking tRPC endpoints in Storybook stories.
 * It integrates with the centralized mock data system to provide consistent,
 * type-safe mock responses.
 */

import { trpcMsw } from "@/api/mock";

import { mockData } from "../factories";

// Create tRPC MSW instance
const t = trpcMsw;

/**
 * Organization handlers
 */
export const organizationHandlers = {
  /**
   * Get many organizations
   */
  getMany: (overrides?: { count?: number; filters?: any }) =>
    t.organizations.getMany.query((req, res, ctx) => {
      const count = overrides?.count ?? 10;
      const organizations = mockData.organizationsPaginated(count);

      return res(ctx.status(200), ctx.data(organizations));
    }),

  /**
   * Get single organization
   */
  get: (organization?: any) =>
    t.organizations.get.query((req, res, ctx) => {
      const org = organization ?? mockData.organization();

      return res(ctx.status(200), ctx.data({ organization: org }));
    }),

  /**
   * Create organization
   */
  create: (response?: any) =>
    t.organizations.create.mutation((req, res, ctx) => {
      const newOrg = response ?? mockData.organization(req.body);

      return res(ctx.status(201), ctx.data(newOrg));
    }),

  /**
   * Update organization
   */
  update: (response?: any) =>
    t.organizations.update.mutation((req, res, ctx) => {
      const updatedOrg =
        response ??
        mockData.organization({
          ...req.body,
          updatedAt: new Date(),
        });

      return res(ctx.status(200), ctx.data(updatedOrg));
    }),

  /**
   * Delete organization
   */
  delete: () =>
    t.organizations.delete.mutation((req, res, ctx) => {
      return res(ctx.status(204));
    }),

  /**
   * Get organization members
   */
  getMembers: (overrides?: { count?: number }) =>
    t.organizations.getMembers.query((req, res, ctx) => {
      const count = overrides?.count ?? 5;
      // TODO: Implement when member factory is available
      const members = { items: [], total: 0 };

      return res(ctx.status(200), ctx.data(members));
    }),

  /**
   * Get organization facilities
   */
  getFacilities: (overrides?: { count?: number }) =>
    t.organizations.getFacilities.query((req, res, ctx) => {
      const count = overrides?.count ?? 3;
      // TODO: Implement when facility factory is available
      const facilities = { items: [], total: 0 };

      return res(ctx.status(200), ctx.data(facilities));
    }),

  /**
   * Get organization jobs
   */
  getJobs: (overrides?: { count?: number }) =>
    t.organizations.getJobs.query((req, res, ctx) => {
      const count = overrides?.count ?? 10;
      // TODO: Implement when job factory is available
      const jobs = { items: [], total: 0 };

      return res(ctx.status(200), ctx.data(jobs));
    }),
};

/**
 * Provider handlers (TODO: Implement when provider factory is ready)
 */
export const providerHandlers = {
  getMany: (overrides?: { count?: number }) =>
    t.providers.getMany.query((req, res, ctx) => {
      // TODO: Implement with provider factory
      const providers = { items: [], total: 0 };
      return res(ctx.status(200), ctx.data(providers));
    }),

  get: (provider?: any) =>
    t.providers.get.query((req, res, ctx) => {
      // TODO: Implement with provider factory
      const prov = provider ?? {};
      return res(ctx.status(200), ctx.data(prov));
    }),
};

/**
 * Job handlers (TODO: Implement when job factory is ready)
 */
export const jobHandlers = {
  getMany: (overrides?: { count?: number }) =>
    t.jobs.getMany.query((req, res, ctx) => {
      // TODO: Implement with job factory
      const jobs = { items: [], total: 0 };
      return res(ctx.status(200), ctx.data(jobs));
    }),

  get: (job?: any) =>
    t.jobs.get.query((req, res, ctx) => {
      // TODO: Implement with job factory
      const j = job ?? {};
      return res(ctx.status(200), ctx.data(j));
    }),
};

/**
 * Application handlers (TODO: Implement when application factory is ready)
 */
export const applicationHandlers = {
  getMany: (overrides?: { count?: number }) =>
    t.applications.getMany.query((req, res, ctx) => {
      // TODO: Implement with application factory
      const applications = { items: [], total: 0 };
      return res(ctx.status(200), ctx.data(applications));
    }),

  get: (application?: any) =>
    t.applications.get.query((req, res, ctx) => {
      // TODO: Implement with application factory
      const app = application ?? {};
      return res(ctx.status(200), ctx.data(app));
    }),
};

/**
 * Shift handlers (TODO: Implement when shift factory is ready)
 */
export const shiftHandlers = {
  getMany: (overrides?: { count?: number }) =>
    t.shifts.getMany.query((req, res, ctx) => {
      // TODO: Implement with shift factory
      const shifts = { items: [], total: 0 };
      return res(ctx.status(200), ctx.data(shifts));
    }),

  get: (shift?: any) =>
    t.shifts.get.query((req, res, ctx) => {
      // TODO: Implement with shift factory
      const s = shift ?? {};
      return res(ctx.status(200), ctx.data(s));
    }),
};

/**
 * Combined handlers for easy import
 */
export const mockHandlers = {
  organizations: organizationHandlers,
  providers: providerHandlers,
  jobs: jobHandlers,
  applications: applicationHandlers,
  shifts: shiftHandlers,

  /**
   * Get all handlers for a complete mock setup
   */
  all: () => [
    ...Object.values(organizationHandlers),
    ...Object.values(providerHandlers),
    ...Object.values(jobHandlers),
    ...Object.values(applicationHandlers),
    ...Object.values(shiftHandlers),
  ],

  /**
   * Get handlers for specific scenarios
   */
  scenario: (name: string) => {
    switch (name) {
      case "organizations-list":
        return [organizationHandlers.getMany()];

      case "organization-detail":
        return [
          organizationHandlers.get(),
          organizationHandlers.getMembers(),
          organizationHandlers.getFacilities(),
          organizationHandlers.getJobs(),
        ];

      case "empty-state":
        return [
          organizationHandlers.getMany({ count: 0 }),
          providerHandlers.getMany({ count: 0 }),
          jobHandlers.getMany({ count: 0 }),
        ];

      default:
        throw new Error(`Unknown scenario: ${name}`);
    }
  },

  /**
   * Create custom handler combinations
   */
  combine: (...handlerGroups: any[][]) => handlerGroups.flat(),
};

/**
 * Utility functions for creating custom handlers
 */
export const handlerUtils = {
  /**
   * Create a delayed response handler
   */
  delayed: (handler: any, delay = 1000) => {
    return (req: any, res: any, ctx: any) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(handler(req, res, ctx));
        }, delay);
      });
    };
  },

  /**
   * Create an error response handler
   */
  error: (status = 500, message = "Internal Server Error") => {
    return (req: any, res: any, ctx: any) => {
      return res(ctx.status(status), ctx.json({ error: message }));
    };
  },

  /**
   * Create a loading state handler (never resolves)
   */
  loading: () => {
    return (req: any, res: any, ctx: any) => {
      return new Promise(() => {}); // Never resolves
    };
  },

  /**
   * Create a paginated response
   */
  paginated: <T>(items: T[], total?: number) => {
    return {
      items,
      total: total ?? items.length,
    };
  },
};

/**
 * Legacy compatibility for existing stories
 */
export const legacyHandlers = {
  /**
   * Convert old MSW handlers to new format
   */
  convert: (oldHandlers: any[]) => {
    console.warn(
      "Using legacy handlers. Please migrate to new mockHandlers API",
    );
    return oldHandlers;
  },

  /**
   * Wrap old handlers with new utilities
   */
  wrap: (oldHandler: any) => {
    console.warn(
      "Using legacy handler wrapper. Please migrate to new mockHandlers API",
    );
    return oldHandler;
  },
};

// Default export
export default mockHandlers;
